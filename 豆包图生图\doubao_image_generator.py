#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
豆包图生图自动化脚本
基于抓包分析实现的完整流程自动化
"""

import requests
import json
import time
import hashlib
import os
import uuid
import hmac
import zlib
from datetime import datetime
from urllib.parse import urlencode, urlparse
import base64
from typing import Optional, Dict, Any

class DoubaoImageGenerator:
    def __init__(self, cookies: str):
        """
        初始化豆包图生图生成器
        
        Args:
            cookies: 从浏览器复制的完整Cookie字符串
        """
        self.session = requests.Session()
        self.cookies = self._parse_cookies(cookies)
        self.session.cookies.update(self.cookies)
        
        # 基础配置
        self.base_headers = {
            'User-Agent': 'Mozilla/5.0 (Linux; Android 10; V2002A Build/QP1A.190711.020) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36',
            'Accept': '*/*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
            'Accept-Encoding': 'gzip, deflate',
            'Origin': 'https://www.doubao.com',
            'Referer': 'https://www.doubao.com/chat/create-image',
            'X-Requested-With': 'mark.via',
        }
        
        # API配置
        self.service_id = "a9rns2rl98"
        self.bot_id = "7338286299411103781"
        self.skill_id = "3"
        
        # 用户信息（需要从Cookie中提取）
        self.user_id = self._extract_user_id()
        
    def _parse_cookies(self, cookie_string: str) -> Dict[str, str]:
        """解析Cookie字符串"""
        cookies = {}
        for item in cookie_string.split(';'):
            if '=' in item:
                key, value = item.strip().split('=', 1)
                cookies[key] = value
        return cookies
    
    def _extract_user_id(self) -> str:
        """从Cookie中提取用户ID"""
        # 从sid_ucp_v1中解析用户ID，这里简化处理
        return "3534286501452377"  # 示例用户ID，实际需要从Cookie解析
    
    def _calculate_crc32(self, data: bytes) -> int:
        """计算文件CRC32校验值"""
        return zlib.crc32(data) & 0xffffffff
    
    def get_upload_auth(self) -> Dict[str, str]:
        """获取上传认证信息"""
        print("🔑 获取上传认证信息...")

        url = "https://www.doubao.com/alice/resource/prepare_upload"
        params = {
            'version_code': '20800',
            'language': 'zh',
            'device_platform': 'web',
            'aid': '497858',
            'real_aid': '497858',
            'pkg_type': 'release_version',
            'device_id': '7468716989062841895',
            'web_id': '7468716986638386703',
            'tea_uuid': '7468716986638386703',
            'use-olympus-account': '1',
            'region': 'CN',
            'sys_region': 'CN',
            'samantha_web': '1',
            'pc_version': '2.24.2',
        }

        data = {
            "tenant_id": "5",
            "scene_id": "5",
            "resource_type": 2
        }

        try:
            response = self.session.post(url, params=params, json=data, timeout=30)
            print(f"🔍 上传认证响应状态码: {response.status_code}")

            if response.status_code != 200:
                print(f"❌ 获取认证失败: {response.status_code}")
                print(f"响应内容: {response.text}")
                raise Exception(f"HTTP {response.status_code}: {response.text}")

            result = response.json()
            if result.get('code') != 0:
                print(f"❌ 认证API返回错误: {result}")
                raise Exception(f"获取上传认证失败: {result}")

            upload_auth = result['data']['upload_auth_token']
            print(f"✅ 获取认证成功")
            return upload_auth
        except requests.exceptions.Timeout:
            print("❌ 请求超时")
            raise Exception("获取上传认证超时")
        except json.JSONDecodeError as e:
            print(f"❌ JSON解析失败: {e}")
            print(f"原始响应: {response.text}")
            raise Exception(f"上传认证响应格式错误: {response.text}")
        except Exception as e:
            print(f"❌ 获取认证异常: {e}")
            raise

    def generate_aws_signature(self, method: str, url: str, upload_auth: Dict, payload: str = "") -> Dict[str, str]:
        """生成AWS4-HMAC-SHA256签名"""
        access_key = upload_auth['access_key']
        secret_key = upload_auth['secret_key']
        session_token = upload_auth['session_token']

        # 解析URL
        parsed = urlparse(url)
        host = parsed.netloc
        path = parsed.path if parsed.path else '/'
        query = parsed.query

        # 时间戳
        t = datetime.utcnow()
        amz_date = t.strftime('%Y%m%dT%H%M%SZ')
        date_stamp = t.strftime('%Y%m%d')

        # 规范化查询字符串
        if query:
            query_params = []
            for param in query.split('&'):
                if '=' in param:
                    key, value = param.split('=', 1)
                    query_params.append((key, value))
                else:
                    query_params.append((param, ''))
            query_params.sort()
            canonical_querystring = '&'.join([f'{k}={v}' for k, v in query_params])
        else:
            canonical_querystring = ''

        # 规范化头部
        canonical_headers = f'host:{host}\nx-amz-date:{amz_date}\nx-amz-security-token:{session_token}\n'
        signed_headers = 'host;x-amz-date;x-amz-security-token'

        # 载荷哈希
        payload_hash = hashlib.sha256(payload.encode('utf-8')).hexdigest()

        # 规范化请求
        canonical_request = f'{method}\n{path}\n{canonical_querystring}\n{canonical_headers}\n{signed_headers}\n{payload_hash}'

        # 创建签名字符串
        algorithm = 'AWS4-HMAC-SHA256'
        credential_scope = f'{date_stamp}/cn-north-1/imagex/aws4_request'
        string_to_sign = f'{algorithm}\n{amz_date}\n{credential_scope}\n{hashlib.sha256(canonical_request.encode()).hexdigest()}'

        # 计算签名
        def sign(key, msg):
            if isinstance(key, str):
                key = key.encode('utf-8')
            return hmac.new(key, msg.encode('utf-8'), hashlib.sha256).digest()

        def get_signature_key(key, date_stamp, region_name, service_name):
            k_date = sign('AWS4' + key, date_stamp)
            k_region = sign(k_date, region_name)
            k_service = sign(k_region, service_name)
            k_signing = sign(k_service, 'aws4_request')
            return k_signing

        signing_key = get_signature_key(secret_key, date_stamp, 'cn-north-1', 'imagex')
        signature = hmac.new(signing_key, string_to_sign.encode('utf-8'), hashlib.sha256).hexdigest()

        # 构建认证头部
        authorization_header = f'{algorithm} Credential={access_key}/{credential_scope}, SignedHeaders={signed_headers}, Signature={signature}'

        return {
            'Authorization': authorization_header,
            'X-Amz-Date': amz_date,
            'x-amz-security-token': session_token
        }
    
    def upload_image(self, image_path: str) -> Optional[str]:
        """
        上传图片到豆包服务器
        
        Args:
            image_path: 本地图片文件路径
            
        Returns:
            上传成功返回图片URI，失败返回None
        """
        try:
            # 读取图片文件
            with open(image_path, 'rb') as f:
                image_data = f.read()
            
            file_size = len(image_data)
            file_ext = os.path.splitext(image_path)[1]
            crc32_value = self._calculate_crc32(image_data)

            print(f"开始上传图片: {image_path}")
            print(f"文件大小: {file_size} 字节")
            print(f"CRC32: {crc32_value:08x}")

            # 1. 获取上传认证信息
            upload_auth = self.get_upload_auth()

            # 2. 申请上传权限
            upload_params = {
                'Action': 'ApplyImageUpload',
                'Version': '2018-08-01',
                'ServiceId': self.service_id,
                'NeedFallback': 'true',
                'FileSize': str(file_size),
                'FileExtension': file_ext,
                's': 'yy49d6n7o6p'
            }

            apply_url = f"https://imagex.bytedanceapi.com/?{urlencode(upload_params)}"

            # 3. 生成AWS签名
            aws_headers = self.generate_aws_signature('GET', apply_url, upload_auth)

            headers = self.base_headers.copy()
            headers.update(aws_headers)
            
            response = self.session.get(apply_url, headers=headers)
            if response.status_code != 200:
                print(f"申请上传失败: {response.status_code}")
                return None
            
            upload_info = response.json()
            if 'Result' not in upload_info:
                print(f"申请上传响应异常: {upload_info}")
                return None
            
            result = upload_info['Result']
            store_info = result['UploadAddress']['StoreInfos'][0]
            upload_host = result['UploadAddress']['UploadHosts'][0]
            
            store_uri = store_info['StoreUri']
            auth_token = store_info['Auth']
            upload_id = store_info['UploadID']
            
            print(f"获取上传权限成功: {store_uri}")
            
            # 4. 上传图片文件
            upload_url = f"https://{upload_host}/upload/v1/{store_uri}"

            upload_headers = {
                'Authorization': auth_token,
                'Content-CRC32': f"{crc32_value:08x}",
                'Content-Type': 'application/octet-stream',
                'X-Storage-U': upload_id,
                'Origin': 'https://www.doubao.com',
                'Referer': 'https://www.doubao.com/chat/',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            }
            
            response = self.session.post(upload_url, data=image_data, headers=upload_headers)
            if response.status_code != 200:
                print(f"上传文件失败: {response.status_code}")
                return None
            
            upload_result = response.json()
            if upload_result.get('code') != 2000:
                print(f"上传文件响应异常: {upload_result}")
                return None
            
            print("文件上传成功")
            
            # 5. 提交上传完成
            commit_params = {
                'Action': 'CommitImageUpload',
                'Version': '2018-08-01',
                'ServiceId': self.service_id
            }

            commit_url = f"https://imagex.bytedanceapi.com/?{urlencode(commit_params)}"

            session_key = result['UploadAddress']['SessionKey']
            commit_payload = json.dumps({"SessionKey": session_key})

            # 生成AWS签名
            commit_aws_headers = self.generate_aws_signature('POST', commit_url, upload_auth, commit_payload)

            commit_headers = {
                'Content-Type': 'application/json',
                'Accept': '*/*',
                'Origin': 'https://www.doubao.com',
                'Referer': 'https://www.doubao.com/chat/',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            }
            commit_headers.update(commit_aws_headers)
            
            response = self.session.post(commit_url, data=commit_payload, headers=commit_headers)
            if response.status_code != 200:
                print(f"提交上传失败: {response.status_code}")
                return None
            
            commit_result = response.json()
            if 'Result' not in commit_result:
                print(f"提交上传响应异常: {commit_result}")
                return None
            
            final_uri = commit_result['Result']['Results'][0]['Uri']
            print(f"图片上传完成: {final_uri}")
            
            return final_uri
            
        except Exception as e:
            print(f"上传图片时发生错误: {e}")
            return None
    
    def generate_image(self, image_uri: str, prompt: str = "更换背景") -> Optional[str]:
        """
        生成图片
        
        Args:
            image_uri: 上传的图片URI
            prompt: 生成提示词
            
        Returns:
            生成成功返回图片URL，失败返回None
        """
        try:
            print(f"开始生成图片，提示词: {prompt}")
            
            # 这里需要实现实际的图生图API调用
            # 由于抓包中没有看到完整的/samantha/chat/completion请求
            # 这里提供一个框架，需要根据实际API调用补充
            
            # 生成对话ID
            conversation_id = str(int(time.time() * 1000))
            
            # 构建请求参数
            base_params = {
                'version_code': '20800',
                'language': 'zh',
                'device_platform': 'web',
                'aid': '497858',
                'real_aid': '497858',
                'pkg_type': 'release_version',
                'device_id': '7468716989062841895',
                'web_id': '7468716986638386703',
                'tea_uuid': '7468716986638386703',
                'use-olympus-account': '1',
                'region': 'CN',
                'sys_region': 'CN',
                'samantha_web': '1',
                'pc_version': '2.24.2'
            }
            
            # 构建请求数据
            request_data = {
                'content_type': 2009,
                'conversation_id': conversation_id,
                'bot_id': self.bot_id,
                'skill_id': self.skill_id,
                'text': prompt,
                'attachments': [{
                    'image_key': image_uri,
                    'refer_types': 'overall'
                }],
                'with_suggest': True,
                'need_create_conversation': True,
                'local_conversation_id': f"local_{int(time.time() * 1000)}"
            }
            
            # 发送请求（这里需要根据实际API补充）
            api_url = f"https://www.doubao.com/samantha/chat/completion?{urlencode(base_params)}"
            
            headers = self.base_headers.copy()
            headers.update({
                'Content-Type': 'application/json',
                'Accept': 'application/json, text/plain, */*'
            })
            
            print("发送图生图请求...")

            # 使用正确的图生图请求格式
            request_data = {
                "messages": [{
                    "content": json.dumps({"text": prompt + "！"}),
                    "content_type": 2009,  # 图生图内容类型
                    "attachments": [{
                        "type": "image",
                        "key": image_uri,
                        "extra": {
                            "refer_types": "overall"
                        },
                        "identifier": str(uuid.uuid4())
                    }],
                    "references": []
                }],
                "completion_option": {
                    "is_regen": False,
                    "with_suggest": False,
                    "need_create_conversation": True,  # 图生图必须创建新会话
                    "launch_stage": 1,
                    "is_replace": False,
                    "is_delete": False,
                    "message_from": 0,
                    "use_auto_cot": False,
                    "resend_for_regen": False,
                    "event_id": "0"
                },
                "conversation_id": "0",  # 图生图时必须使用"0"创建新会话
                "local_conversation_id": f"local_{int(time.time() * 1000000)}",
                "local_message_id": str(uuid.uuid4())
            }

            headers = self.base_headers.copy()
            headers.update({
                'Content-Type': 'application/json',
                'Accept': 'application/json, text/plain, */*',
                'x-flow-trace': f"04-{uuid.uuid4().hex[:16]}-{uuid.uuid4().hex[:16]}-01",
                'Agw-Js-Conv': 'str',
                'last-event-id': 'undefined',
                'Sec-Fetch-Site': 'same-origin',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Dest': 'empty'
            })

            response = self.session.post(api_url, params=base_params, json=request_data, headers=headers, stream=True)

            if response.status_code == 200:
                print("✅ 请求成功！正在解析响应...")

                # 解析流式响应
                generated_images = []
                count = 0
                start_time = time.time()

                for line in response.iter_lines(decode_unicode=True):
                    if line.startswith('data: '):
                        count += 1
                        current_time = time.time()
                        elapsed = int(current_time - start_time)

                        # 显示处理进度
                        if count % 100 == 0 and count > 0:
                            print(f"⏳ 已处理 {count} 行响应，总耗时 {elapsed} 秒...")

                        try:
                            data_str = line[6:]  # 去掉 'data: ' 前缀
                            if data_str.strip():
                                event_data = json.loads(data_str)

                                if 'event_data' in event_data:
                                    inner_data = json.loads(event_data['event_data'])

                                    # 检查错误
                                    error_code = inner_data.get('code')
                                    if error_code and str(error_code).startswith('71002'):
                                        print(f"❌ 检测到限制错误: {error_code}")
                                        return None

                                    # 解析图片生成结果
                                    if 'message' in inner_data and 'content' in inner_data['message']:
                                        content = json.loads(inner_data['message']['content'])

                                        if 'creations' in content:
                                            for creation in content['creations']:
                                                if creation.get('type') == 1 and 'image' in creation:
                                                    image_info = creation['image']
                                                    status = image_info.get('status')

                                                    if status == 1:
                                                        print("🔄 图片生成中...")
                                                    elif status == 2:  # 生成完成
                                                        print(f"🎨 图片生成完成！耗时 {elapsed} 秒")
                                                        urls = {
                                                            'thumb': image_info.get('image_thumb', {}).get('url'),
                                                            'original': image_info.get('image_ori', {}).get('url'),
                                                            'raw': image_info.get('image_raw', {}).get('url'),
                                                            'preview': image_info.get('preview_img', {}).get('url')
                                                        }

                                                        valid_urls = {k: v for k, v in urls.items() if v}
                                                        if valid_urls:
                                                            generated_images.append(valid_urls)
                                                            print(f"生成了 {len(valid_urls)} 种格式的图片")
                                                            for name, url in valid_urls.items():
                                                                print(f"  {name}: {url}")
                                                            return list(valid_urls.values())[0]  # 返回第一个有效URL
                                                    elif status == 3:  # 生成失败
                                                        print("❌ 图片生成失败")
                                                        return None
                        except:
                            pass

                        # 超时检查
                        if elapsed > 300:  # 5分钟超时
                            print("⏰ 已达到最大等待时间（5分钟），停止等待")
                            break

                return None
            else:
                print(f"❌ 请求失败: {response.status_code}")
                print(f"错误响应: {response.text}")
                return None
            
        except Exception as e:
            print(f"生成图片时发生错误: {e}")
            return None
    
    def process_image(self, image_path: str, prompt: str = "更换背景") -> Optional[str]:
        """
        完整的图片处理流程
        
        Args:
            image_path: 本地图片文件路径
            prompt: 生成提示词
            
        Returns:
            生成成功返回图片URL，失败返回None
        """
        # 1. 上传图片
        image_uri = self.upload_image(image_path)
        if not image_uri:
            return None
        
        # 2. 生成图片
        result_url = self.generate_image(image_uri, prompt)
        return result_url

def main():
    """主函数示例"""
    # 从浏览器复制完整的Cookie字符串
    cookies = """d_ticket=c387aa7a25690088d32e0f295a9faa5828090; odin_tt=adc3604237fa735eef5d9b2726da716e7ea5dd617e5184147618251da72231aa77815405b56975c824529748a009f1f85cf25c9d17928d11725b0ef3d9e34d33; uid_tt=71dd29fd50dbf8f1a1d7b3090d765234; sessionid=9b6a807194da06d0c111bb246ff92247; sid_guard=9b6a807194da06d0c111bb246ff92247%7C1750354436%7C5184000%7CMon%2C+18-Aug-2025+17%3A33%3A56+GMT"""

    # 创建生成器实例
    generator = DoubaoImageGenerator(cookies)

    # 固定使用指定的图片路径
    image_path = r"C:\DBE25C6475AF6852691B040206E94167.jpg"
    prompt = "更换背景"

    # 检查图片是否存在
    if not os.path.exists(image_path):
        print(f"错误：指定的图片文件不存在: {image_path}")
        print("请确保图片文件存在于指定路径")
        return

    print(f"使用固定图片: {image_path}")
    result = generator.process_image(image_path, prompt)
    if result:
        print(f"生成成功: {result}")
    else:
        print("生成失败")

if __name__ == "__main__":
    main()
